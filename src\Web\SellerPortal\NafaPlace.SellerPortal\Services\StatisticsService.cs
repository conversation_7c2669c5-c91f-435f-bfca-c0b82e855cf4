using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.JSInterop;
using NafaPlace.SellerPortal.Models.Statistics;
using System.IdentityModel.Tokens.Jwt;

namespace NafaPlace.SellerPortal.Services;

// DTOs pour les nouveaux endpoints
public class SellerStatisticsDto
{
    public int TotalOrders { get; set; }
    public decimal TotalRevenue { get; set; }
    public int PendingOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int CancelledOrders { get; set; }
    public decimal AverageOrderValue { get; set; }
    public int RecentOrdersCount { get; set; }
    public List<TopProductDto> TopSellingProducts { get; set; } = new();
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public int TotalQuantitySold { get; set; }
    public decimal TotalRevenue { get; set; }
}

public class RevenueStatisticsDto
{
    public decimal TotalRevenue { get; set; }
    public List<MonthlyRevenueDto> MonthlyRevenue { get; set; } = new();
    public decimal AverageMonthlyRevenue { get; set; }
    public MonthlyRevenueDto? BestMonth { get; set; }
    public decimal GrowthRate { get; set; }
}

public class MonthlyRevenueDto
{
    public int Year { get; set; }
    public int Month { get; set; }
    public decimal Revenue { get; set; }
    public int OrderCount { get; set; }
    public string MonthName => new DateTime(Year, Month, 1).ToString("MMMM yyyy");
}

public class TopProductDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int TotalQuantitySold { get; set; }
    public decimal TotalRevenue { get; set; }
}

public class StatisticsService : IStatisticsService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IJSRuntime _jsRuntime;
    private readonly IConfiguration _configuration;

    public StatisticsService(IHttpClientFactory httpClientFactory, IJSRuntime jsRuntime, IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;
        _jsRuntime = jsRuntime;
        _configuration = configuration;
    }

    public async Task<DashboardStatistics> GetDashboardStatisticsAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return GetEmptyDashboardStatistics();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];

            // Utiliser les nouveaux endpoints spécifiques aux vendeurs
            var statisticsResponse = await httpClient.GetAsync($"{apiUrl}/api/statistics/seller/{sellerId}");
            var revenueResponse = await httpClient.GetAsync($"{apiUrl}/api/statistics/seller/{sellerId}/revenue");

            if (statisticsResponse.IsSuccessStatusCode && revenueResponse.IsSuccessStatusCode)
            {
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                var statisticsContent = await statisticsResponse.Content.ReadAsStringAsync();
                var revenueContent = await revenueResponse.Content.ReadAsStringAsync();

                var sellerStats = JsonSerializer.Deserialize<SellerStatisticsDto>(statisticsContent, options);
                var revenueStats = JsonSerializer.Deserialize<RevenueStatisticsDto>(revenueContent, options);

                return ConvertToSellerDashboardStatistics(sellerStats, revenueStats);
            }

            // Retourner des statistiques vides au lieu des données de démonstration
            return GetEmptyDashboardStatistics();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des statistiques du dashboard: {ex.Message}");
            // Retourner des statistiques vides au lieu des données de démonstration
            return GetEmptyDashboardStatistics();
        }
    }

    public async Task<SalesStatistics> GetSalesStatisticsAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new SalesStatistics();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];

            // Utiliser l'endpoint existant pour les statistiques du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/seller/{sellerId}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var sellerStats = JsonSerializer.Deserialize<SellerStatisticsDto>(content, options);

                if (sellerStats != null)
                {
                    return new SalesStatistics
                    {
                        TotalSales = sellerStats.TotalRevenue,
                        TotalOrders = sellerStats.TotalOrders,
                        AverageOrderValue = sellerStats.AverageOrderValue,
                        SalesGrowth = 0, // Calculé plus tard si nécessaire
                        OrdersGrowth = 0,
                        AovGrowth = 0,
                        ConversionRate = 0,
                        ConversionGrowth = 0
                    };
                }
            }

            // Retourner des statistiques vides
            return new SalesStatistics();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des statistiques de vente: {ex.Message}");
            return new SalesStatistics();
        }
    }

    public async Task<List<TopProduct>> GetTopProductsAsync(StatisticsRequest request, int limit = 10)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new List<TopProduct>();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];

            // Utiliser l'endpoint existant pour les statistiques du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/seller/{sellerId}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var sellerStats = JsonSerializer.Deserialize<SellerStatisticsDto>(content, options);

                if (sellerStats?.TopSellingProducts != null)
                {
                    return sellerStats.TopSellingProducts.Take(limit).Select(p => new TopProduct
                    {
                        ProductId = p.ProductId,
                        Name = p.ProductName,
                        UnitsSold = p.TotalQuantitySold,
                        Revenue = p.TotalRevenue,
                        ImageUrl = "", // Pas disponible dans l'API actuelle
                        CategoryName = "" // Pas disponible dans l'API actuelle
                    }).ToList();
                }
            }

            // Retourner une liste vide
            return new List<TopProduct>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des top produits: {ex.Message}");
            return new List<TopProduct>();
        }
    }

    public async Task<List<RegionSales>> GetSalesByRegionAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new List<RegionSales>();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var queryParams = BuildQueryParams(request);

            // Utiliser l'endpoint existant pour les commandes du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders/seller/{sellerId}{queryParams}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var orders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();

                // Calculer les ventes par région à partir des commandes
                return CalculateRegionSalesFromOrders(orders);
            }

            // Retourner une liste vide
            return new List<RegionSales>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des ventes par région: {ex.Message}");
            return new List<RegionSales>();
        }
    }

    public async Task<List<CategorySales>> GetSalesByCategoryAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new List<CategorySales>();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var queryParams = BuildQueryParams(request);

            // Utiliser l'endpoint existant pour les commandes du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders/seller/{sellerId}{queryParams}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var orders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();

                // Calculer les ventes par catégorie à partir des commandes
                return CalculateCategorySalesFromOrders(orders);
            }

            // Retourner une liste vide
            return new List<CategorySales>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des ventes par catégorie: {ex.Message}");
            return new List<CategorySales>();
        }
    }

    public async Task<List<SalesChartData>> GetSalesChartDataAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new List<SalesChartData>();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var queryParams = BuildQueryParams(request);

            // Utiliser l'endpoint existant pour les commandes du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders/seller/{sellerId}{queryParams}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var orders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();

                // Calculer les données du graphique à partir des commandes
                return CalculateSalesChartFromOrders(orders, request);
            }

            // Retourner une liste vide
            return new List<SalesChartData>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des données du graphique: {ex.Message}");
            return new List<SalesChartData>();
        }
    }

    public async Task<List<SalesReport>> GetSalesReportAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new List<SalesReport>();
            }

            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var queryParams = BuildQueryParams(request);

            // Utiliser l'endpoint existant pour les commandes du vendeur
            var response = await httpClient.GetAsync($"{apiUrl}/api/orders/seller/{sellerId}{queryParams}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var orders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();

                // Calculer le rapport de ventes à partir des commandes
                return CalculateSalesReportFromOrders(orders, request);
            }

            // Retourner une liste vide
            return new List<SalesReport>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du rapport de ventes: {ex.Message}");
            return new List<SalesReport>();
        }
    }

    public async Task<byte[]> ExportStatisticsAsync(ExportRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PostAsync($"{apiUrl}/api/statistics/export", content);
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsByteArrayAsync();
            }
            
            return Array.Empty<byte>();
        }
        catch (Exception)
        {
            return Array.Empty<byte>();
        }
    }

    private async Task SetAuthorizationHeaderAsync(HttpClient httpClient)
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(token))
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }
        catch (Exception)
        {
            // Handle JS interop errors silently
        }
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
                return 0;

            // Décoder le JWT pour récupérer le SellerId
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }

            // Si pas de SellerId dans le token, retourner 0 pour indiquer qu'aucun vendeur n'est trouvé
            return 0;
        }
        catch (Exception)
        {
            return 0; // Retourner 0 pour indiquer qu'aucun vendeur n'est trouvé
        }
    }

    private string BuildQueryParams(StatisticsRequest request)
    {
        var queryParams = new List<string>();
        
        if (request.StartDate.HasValue)
            queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");
        if (request.EndDate.HasValue)
            queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");
        if (request.SellerId.HasValue)
            queryParams.Add($"sellerId={request.SellerId.Value}");
        if (!string.IsNullOrEmpty(request.Period))
            queryParams.Add($"period={request.Period}");

        return queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
    }

    private DashboardStatistics GetEmptyDashboardStatistics()
    {
        return new DashboardStatistics
        {
            SalesStats = new SalesStatistics(),
            TopProducts = new List<TopProduct>(),
            SalesByRegion = new List<RegionSales>(),
            SalesByCategory = new List<CategorySales>(),
            SalesChartData = new List<SalesChartData>(),
            RecentSales = new List<SalesReport>()
        };
    }

    private DashboardStatistics GetDemoDashboardStatistics()
    {
        return new DashboardStatistics
        {
            SalesStats = GetDemoSalesStatistics(),
            TopProducts = GetDemoTopProducts(),
            SalesByRegion = GetDemoRegionSales(),
            SalesByCategory = GetDemoCategorySales(),
            SalesChartData = GetDemoSalesChartData(new StatisticsRequest { Period = "30" }),
            RecentSales = GetDemoSalesReport(new StatisticsRequest { StartDate = DateTime.Now.AddDays(-7), EndDate = DateTime.Now })
        };
    }

    private SalesStatistics GetDemoSalesStatistics()
    {
        return new SalesStatistics
        {
            TotalSales = 3250000,
            SalesGrowth = 12.5,
            TotalOrders = 45,
            OrdersGrowth = 8.2,
            AverageOrderValue = 72222,
            AovGrowth = 4.3,
            ConversionRate = 3.8,
            ConversionGrowth = -0.5
        };
    }

    private List<TopProduct> GetDemoTopProducts()
    {
        return new List<TopProduct>
        {
            new TopProduct { ProductId = 1, Name = "Smartphone XYZ", UnitsSold = 15, Revenue = 2250000, CategoryName = "Électronique", ImageUrl = "/images/products/smartphone.jpg" },
            new TopProduct { ProductId = 2, Name = "Écouteurs Sans Fil", UnitsSold = 12, Revenue = 300000, CategoryName = "Électronique", ImageUrl = "/images/products/headphones.jpg" },
            new TopProduct { ProductId = 3, Name = "T-shirt Coton", UnitsSold = 25, Revenue = 200000, CategoryName = "Vêtements", ImageUrl = "/images/products/tshirt.jpg" },
            new TopProduct { ProductId = 4, Name = "Tablette 10 pouces", UnitsSold = 5, Revenue = 600000, CategoryName = "Électronique", ImageUrl = "/images/products/tablet.jpg" },
            new TopProduct { ProductId = 5, Name = "Crème Hydratante", UnitsSold = 18, Revenue = 135000, CategoryName = "Beauté", ImageUrl = "/images/products/cream.jpg" }
        };
    }

    private List<RegionSales> GetDemoRegionSales()
    {
        return new List<RegionSales>
        {
            new RegionSales { Name = "Conakry", OrderCount = 15, Revenue = 1200000, Percentage = 36.9 },
            new RegionSales { Name = "Dakar", OrderCount = 12, Revenue = 950000, Percentage = 29.2 },
            new RegionSales { Name = "Bamako", OrderCount = 8, Revenue = 650000, Percentage = 20.0 },
            new RegionSales { Name = "Abidjan", OrderCount = 10, Revenue = 450000, Percentage = 13.9 }
        };
    }

    private List<CategorySales> GetDemoCategorySales()
    {
        return new List<CategorySales>
        {
            new CategorySales { CategoryId = 1, CategoryName = "Électronique", Revenue = 3150000, OrderCount = 32, Percentage = 96.9, Color = "#0d6efd" },
            new CategorySales { CategoryId = 2, CategoryName = "Vêtements", Revenue = 200000, OrderCount = 8, Percentage = 6.2, Color = "#fd7e14" },
            new CategorySales { CategoryId = 3, CategoryName = "Beauté", Revenue = 135000, OrderCount = 5, Percentage = 4.2, Color = "#198754" }
        };
    }

    private List<SalesChartData> GetDemoSalesChartData(StatisticsRequest request)
    {
        var data = new List<SalesChartData>();
        var days = int.Parse(request.Period ?? "30");
        var random = new Random(123);

        for (int i = days; i >= 0; i--)
        {
            var date = DateTime.Now.AddDays(-i);
            var sales = random.Next(50000, 200000);
            var orders = random.Next(1, 8);

            data.Add(new SalesChartData
            {
                Date = date,
                Sales = sales,
                Orders = orders
            });
        }

        return data;
    }

    private List<SalesReport> GetDemoSalesReport(StatisticsRequest request)
    {
        var reports = new List<SalesReport>();
        var startDate = request.StartDate ?? DateTime.Now.AddDays(-7);
        var endDate = request.EndDate ?? DateTime.Now;
        var currentDate = startDate;
        var random = new Random(123);

        while (currentDate <= endDate)
        {
            var orderCount = random.Next(1, 8);
            var productsSold = random.Next(orderCount, orderCount * 3);
            var revenue = productsSold * (random.Next(5000, 20000));
            
            reports.Add(new SalesReport
            {
                Date = currentDate,
                OrderCount = orderCount,
                ProductsSold = productsSold,
                Revenue = revenue,
                AverageOrderValue = orderCount > 0 ? revenue / orderCount : 0
            });
            
            currentDate = currentDate.AddDays(1);
        }

        return reports;
    }

    private DashboardStatistics CalculateStatisticsFromOrders(List<ApiOrderDto> orders, StatisticsRequest request)
    {
        var now = DateTime.Now;
        var startOfMonth = new DateTime(now.Year, now.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

        // Filtrer les commandes selon la période demandée
        var filteredOrders = orders.Where(o =>
            (!request.StartDate.HasValue || o.OrderDate >= request.StartDate.Value) &&
            (!request.EndDate.HasValue || o.OrderDate <= request.EndDate.Value)
        ).ToList();

        // Calculer les statistiques
        var totalSales = filteredOrders.Where(o => o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid").Sum(o => o.TotalAmount);
        var totalOrders = filteredOrders.Count;
        var totalRevenue = filteredOrders.Sum(o => o.TotalAmount);
        var conversionRate = totalOrders > 0 ? (double)filteredOrders.Count(o => o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid") / totalOrders * 100 : 0;

        // Calculer les commandes du mois précédent pour la comparaison
        var lastMonthStart = startOfMonth.AddMonths(-1);
        var lastMonthEnd = startOfMonth.AddDays(-1);
        var lastMonthOrders = orders.Where(o => o.OrderDate >= lastMonthStart && o.OrderDate <= lastMonthEnd).ToList();
        var lastMonthSales = lastMonthOrders.Where(o => o.PaymentStatus == "Completed" || o.PaymentStatus == "Paid").Sum(o => o.TotalAmount);

        // Calculer le pourcentage de croissance
        var salesGrowth = lastMonthSales > 0 ? (double)(totalSales - lastMonthSales) / (double)lastMonthSales * 100 : 0;

        return new DashboardStatistics
        {
            SalesStats = new SalesStatistics
            {
                TotalSales = totalSales,
                SalesGrowth = salesGrowth,
                TotalOrders = totalOrders,
                OrdersGrowth = salesGrowth, // Simplification
                AverageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0,
                AovGrowth = 0, // Simplification
                ConversionRate = conversionRate,
                ConversionGrowth = 0 // Simplification
            },
            TopProducts = new List<TopProduct>(),
            SalesByRegion = new List<RegionSales>(),
            SalesByCategory = new List<CategorySales>(),
            SalesChartData = new List<SalesChartData>(),
            RecentSales = new List<SalesReport>()
        };
    }

    private List<RegionSales> CalculateRegionSalesFromOrders(List<ApiOrderDto> orders)
    {
        var regionSales = orders
            .Where(o => o.ShippingAddress != null)
            .GroupBy(o => o.ShippingAddress.City ?? "Inconnu")
            .Select(g => new RegionSales
            {
                Name = g.Key,
                OrderCount = g.Count(),
                Revenue = g.Sum(o => o.TotalAmount),
                Percentage = 0 // Sera calculé après
            })
            .OrderByDescending(r => r.Revenue)
            .Take(10)
            .ToList();

        // Calculer les pourcentages
        var totalRevenue = regionSales.Sum(r => r.Revenue);
        if (totalRevenue > 0)
        {
            foreach (var region in regionSales)
            {
                region.Percentage = (double)(region.Revenue / totalRevenue) * 100;
            }
        }

        return regionSales;
    }

    private List<CategorySales> CalculateCategorySalesFromOrders(List<ApiOrderDto> orders)
    {
        var categorySales = orders
            .SelectMany(o => o.OrderItems)
            .GroupBy(oi => oi.CategoryName ?? "Inconnu")
            .Select(g => new CategorySales
            {
                CategoryName = g.Key,
                Revenue = g.Sum(oi => oi.UnitPrice * oi.Quantity),
                OrderCount = g.Select(oi => oi.OrderId).Distinct().Count(),
                Color = GetRandomColor()
            })
            .ToList();

        var totalRevenue = categorySales.Sum(c => c.Revenue);
        foreach (var category in categorySales)
        {
            category.Percentage = totalRevenue > 0 ? (double)(category.Revenue / totalRevenue * 100) : 0;
        }

        return categorySales.OrderByDescending(c => c.Revenue).ToList();
    }

    private string GetRandomColor()
    {
        var colors = new[] { "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40" };
        var random = new Random();
        return colors[random.Next(colors.Length)];
    }

    private List<SalesChartData> CalculateSalesChartFromOrders(List<ApiOrderDto> orders, StatisticsRequest request)
    {
        var startDate = request.StartDate ?? DateTime.Now.AddDays(-30);
        var endDate = request.EndDate ?? DateTime.Now;

        var chartData = new List<SalesChartData>();

        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var dayOrders = orders.Where(o => o.OrderDate.Date == date).ToList();

            chartData.Add(new SalesChartData
            {
                Date = date,
                Sales = dayOrders.Sum(o => o.TotalAmount),
                Orders = dayOrders.Count
            });
        }

        return chartData.OrderBy(c => c.Date).ToList();
    }

    private List<SalesReport> CalculateSalesReportFromOrders(List<ApiOrderDto> orders, StatisticsRequest request)
    {
        var salesReport = orders
            .GroupBy(o => o.OrderDate.Date)
            .Select(g => new SalesReport
            {
                Date = g.Key,
                OrderCount = g.Count(),
                ProductsSold = g.SelectMany(o => o.OrderItems ?? new List<ApiOrderItemDto>()).Sum(i => i.Quantity),
                Revenue = g.Sum(o => o.TotalAmount),
                AverageOrderValue = g.Count() > 0 ? g.Sum(o => o.TotalAmount) / g.Count() : 0
            })
            .OrderByDescending(r => r.Date)
            .Take(30)
            .ToList();

        return salesReport;
    }

    private DashboardStatistics ConvertToSellerDashboardStatistics(SellerStatisticsDto? sellerStats, RevenueStatisticsDto? revenueStats)
    {
        if (sellerStats == null || revenueStats == null)
        {
            return GetEmptyDashboardStatistics();
        }

        return new DashboardStatistics
        {
            SalesStats = new SalesStatistics
            {
                TotalSales = sellerStats.TotalRevenue,
                SalesGrowth = (double)revenueStats.GrowthRate,
                TotalOrders = sellerStats.TotalOrders,
                OrdersGrowth = 0, // Simplification
                AverageOrderValue = sellerStats.AverageOrderValue,
                AovGrowth = 0, // Simplification
                ConversionRate = sellerStats.TotalOrders > 0 ? (double)sellerStats.CompletedOrders / sellerStats.TotalOrders * 100 : 0,
                ConversionGrowth = 0 // Simplification
            },
            TopProducts = sellerStats.TopSellingProducts.Select(p => new TopProduct
            {
                ProductId = p.ProductId,
                Name = p.ProductName,
                UnitsSold = p.TotalQuantitySold,
                Revenue = p.TotalRevenue,
                CategoryName = "Catégorie", // Simplification
                ImageUrl = "/images/products/default.jpg" // Simplification
            }).ToList(),
            SalesByRegion = new List<RegionSales>(),
            SalesByCategory = new List<CategorySales>(),
            SalesChartData = revenueStats.MonthlyRevenue.Select(m => new SalesChartData
            {
                Date = new DateTime(m.Year, m.Month, 1),
                Sales = m.Revenue,
                Orders = m.OrderCount
            }).ToList(),
            RecentSales = new List<SalesReport>()
        };
    }
}
