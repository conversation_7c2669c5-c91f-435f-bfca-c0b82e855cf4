using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.JSInterop;
using NafaPlace.SellerPortal.Models.Orders;
using System.IdentityModel.Tokens.Jwt;

namespace NafaPlace.SellerPortal.Services;

// DTOs pour l'API Order
public class ApiOrderDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public DateTime OrderDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // String au lieu d'int
    public string PaymentMethod { get; set; } = string.Empty; // String au lieu d'int
    public string PaymentStatus { get; set; } = string.Empty; // String au lieu d'int
    public string? PaymentTransactionId { get; set; }
    public DateTime? PaymentDate { get; set; }
    public ApiShippingAddressDto? ShippingAddress { get; set; }
    public List<ApiOrderItemDto>? OrderItems { get; set; }
}

public class ApiOrderItemDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
}

public class ApiShippingAddressDto
{
    public string FullName { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
}

public class OrderService : IOrderService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IJSRuntime _jsRuntime;
    private readonly IConfiguration _configuration;

    public OrderService(IHttpClientFactory httpClientFactory, IJSRuntime jsRuntime, IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;
        _jsRuntime = jsRuntime;
        _configuration = configuration;
    }

    public async Task<OrdersPagedResponse> GetOrdersAsync(OrderFilterRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            // Récupérer l'ID du vendeur connecté via AuthService (comme StatisticsService)
            var sellerId = await GetCurrentSellerIdViaAuthServiceAsync();
            Console.WriteLine($"[OrderService] SellerId récupéré via AuthService: {sellerId}");

            if (sellerId == 0)
            {
                Console.WriteLine("[OrderService] ❌ SellerId = 0, retour de liste vide");
                return new OrdersPagedResponse
                {
                    Orders = new List<Order>(),
                    TotalCount = 0,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={Uri.EscapeDataString(request.Status)}");
            if (request.StartDate.HasValue)
                queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");
            if (request.EndDate.HasValue)
                queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");

            queryParams.Add($"pageNumber={request.PageNumber}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";

            // Utiliser l'endpoint spécifique au vendeur (BaseAddress déjà configurée dans le client nommé)
            var url = $"/api/orders/seller/{sellerId}{queryString}";
            Console.WriteLine($"[OrderService] Appel API: {url}");
            var response = await httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[OrderService] Réponse API: {content}");
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                // L'API retourne IEnumerable<Domain.Order>, nous devons le convertir
                var apiOrders = JsonSerializer.Deserialize<List<ApiOrderDto>>(content, options) ?? new List<ApiOrderDto>();
                Console.WriteLine($"[OrderService] Nombre de commandes reçues: {apiOrders.Count}");

                // Convertir vers notre modèle
                var orders = apiOrders.Select(apiOrder => ConvertToOrder(apiOrder, request.SellerId ?? 0)).ToList();
                Console.WriteLine($"[OrderService] Nombre de commandes converties: {orders.Count}");

                return new OrdersPagedResponse
                {
                    Orders = orders,
                    TotalCount = orders.Count,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }

            // Retourner une réponse vide au lieu des données de démonstration
            return new OrdersPagedResponse
            {
                Orders = new List<Order>(),
                TotalCount = 0,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des commandes: {ex.Message}");
            // Retourner une réponse vide au lieu des données de démonstration
            return new OrdersPagedResponse
            {
                Orders = new List<Order>(),
                TotalCount = 0,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize
            };
        }
    }

    public async Task<Order?> GetOrderByIdAsync(int orderId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.GetAsync($"/api/orders/{orderId}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var apiOrder = JsonSerializer.Deserialize<ApiOrderDto>(content, options);

                if (apiOrder != null)
                {
                    return ConvertToOrder(apiOrder);
                }
            }

            // Retourner null si la commande n'est pas trouvée
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération de la commande {orderId}: {ex.Message}");
            // Retourner null en cas d'erreur
            return null;
        }
    }

    public async Task<bool> UpdateOrderStatusAsync(UpdateOrderStatusRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"/api/orders/{request.OrderId}/status", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> UpdatePaymentStatusAsync(UpdatePaymentStatusRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"/api/orders/{request.OrderId}/payment-status", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> UpdateOrderNotesAsync(int orderId, string notes)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var request = new { Notes = notes };
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PutAsync($"/api/orders/{orderId}/notes", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> CancelOrderAsync(int orderId)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var response = await httpClient.PutAsync($"/api/orders/{orderId}/cancel", null);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<byte[]> ExportOrdersAsync(OrderFilterRequest request, string format = "excel")
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("OrderApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var exportRequest = new
            {
                request.SearchTerm,
                request.Status,
                request.StartDate,
                request.EndDate,
                request.SellerId,
                Format = format
            };

            var json = JsonSerializer.Serialize(exportRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync($"/api/orders/export", content);
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsByteArrayAsync();
            }
            
            return Array.Empty<byte>();
        }
        catch (Exception)
        {
            return Array.Empty<byte>();
        }
    }

    private async Task SetAuthorizationHeaderAsync(HttpClient httpClient)
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            Console.WriteLine($"[OrderService] Token récupéré: {(string.IsNullOrEmpty(token) ? "VIDE" : $"longueur={token.Length}")}");

            if (!string.IsNullOrEmpty(token))
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                Console.WriteLine($"[OrderService] ✅ En-tête Authorization ajouté");
            }
            else
            {
                Console.WriteLine($"[OrderService] ❌ Token vide, pas d'en-tête Authorization");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[OrderService] ❌ Erreur lors de la récupération du token: {ex.Message}");
        }
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (string.IsNullOrEmpty(token))
                return 0;

            // Décoder le JWT pour récupérer le SellerId
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }

            // Si pas de SellerId dans le token, retourner 0 pour indiquer qu'aucun vendeur n'est trouvé
            return 0;
        }
        catch (Exception)
        {
            return 0; // Retourner 0 pour indiquer qu'aucun vendeur n'est trouvé
        }
    }

    private async Task<int> GetCurrentSellerIdViaAuthServiceAsync()
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            Console.WriteLine($"[OrderService] Token récupéré: {(string.IsNullOrEmpty(token) ? "null/empty" : $"longueur={token.Length}")}");

            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine("[OrderService] ❌ Aucun token trouvé dans localStorage");
                return 0;
            }

            // Vérifier le format du token avant de le décoder
            var tokenParts = token.Split('.');
            Console.WriteLine($"[OrderService] Token parts count: {tokenParts.Length}");

            if (!token.Contains('.') || tokenParts.Length != 3)
            {
                Console.WriteLine($"[OrderService] ❌ Token mal formé: {token.Substring(0, Math.Min(50, token.Length))}...");
                Console.WriteLine($"[OrderService] Token parts: {string.Join(" | ", tokenParts.Take(3))}");
                return 0;
            }

            // Afficher les premières parties du token pour debug
            Console.WriteLine($"[OrderService] Token header: {tokenParts[0].Substring(0, Math.Min(20, tokenParts[0].Length))}...");
            Console.WriteLine($"[OrderService] Token payload: {tokenParts[1].Substring(0, Math.Min(20, tokenParts[1].Length))}...");

            // Essayer de décoder manuellement le payload pour contourner les problèmes de CanReadToken
            int userId = 0;
            try
            {
                var payload = tokenParts[1];
                // Ajouter le padding nécessaire pour Base64
                switch (payload.Length % 4)
                {
                    case 2: payload += "=="; break;
                    case 3: payload += "="; break;
                }

                var payloadBytes = Convert.FromBase64String(payload);
                var payloadJson = System.Text.Encoding.UTF8.GetString(payloadBytes);
                Console.WriteLine($"[OrderService] Payload décodé: {payloadJson}");

                // Parser le JSON pour récupérer directement le sub (userId)
                var payloadDoc = JsonDocument.Parse(payloadJson);
                if (payloadDoc.RootElement.TryGetProperty("sub", out var subElement))
                {
                    var userIdStr = subElement.GetString();
                    Console.WriteLine($"[OrderService] UserId trouvé dans payload: {userIdStr}");

                    if (int.TryParse(userIdStr, out userId))
                    {
                        Console.WriteLine($"[OrderService] UserId parsé avec succès: {userId}");
                    }
                    else
                    {
                        Console.WriteLine($"[OrderService] ❌ Impossible de parser userId: {userIdStr}");
                        return 0;
                    }
                }
                else
                {
                    Console.WriteLine("[OrderService] ❌ Propriété 'sub' non trouvée dans le payload");
                    return 0;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OrderService] ❌ Erreur lors du décodage manuel: {ex.Message}");

                // Fallback vers la méthode standard avec JwtSecurityTokenHandler
                try
                {
                    var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();

                    if (!handler.CanReadToken(token))
                    {
                        Console.WriteLine("[OrderService] ❌ Token JWT non lisible par CanReadToken");
                        return 0;
                    }

                    var jsonToken = handler.ReadJwtToken(token);
                    Console.WriteLine($"[OrderService] Token décodé avec succès, claims: {jsonToken.Claims.Count()}");

                    var userIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub" || x.Type == "userId");
                    if (userIdClaim == null)
                    {
                        Console.WriteLine("[OrderService] ❌ Claim userId/sub non trouvé");
                        // Afficher tous les claims pour debug
                        foreach (var claim in jsonToken.Claims)
                        {
                            Console.WriteLine($"[OrderService] Claim: {claim.Type} = {claim.Value}");
                        }
                        return 0;
                    }

                    if (!int.TryParse(userIdClaim.Value, out userId))
                    {
                        Console.WriteLine($"[OrderService] ❌ UserId non parsable: {userIdClaim.Value}");
                        return 0;
                    }

                    Console.WriteLine($"[OrderService] UserId du token (fallback): {userId}");
                }
                catch (Exception fallbackEx)
                {
                    Console.WriteLine($"[OrderService] ❌ Fallback également échoué: {fallbackEx.Message}");
                    return 0;
                }
            }

            // Récupérer le vendeur via l'API Catalog (comme StatisticsService)
            using var httpClient = _httpClientFactory.CreateClient("CatalogApi");
            await SetAuthorizationHeaderAsync(httpClient);

            var url = $"/api/v1/sellers/by-user/{userId}";
            Console.WriteLine($"[OrderService] Appel API Catalog: {url}");

            var response = await httpClient.GetAsync(url);
            Console.WriteLine($"[OrderService] Réponse API Catalog: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[OrderService] Contenu réponse: {content}");

                var seller = JsonSerializer.Deserialize<SellerDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                Console.WriteLine($"[OrderService] Vendeur trouvé: ID={seller?.Id}, Nom={seller?.Name}");
                return seller?.Id ?? 0;
            }
            else
            {
                Console.WriteLine($"[OrderService] ❌ Erreur API Catalog: {response.StatusCode}");
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[OrderService] ❌ Détail erreur: {errorContent}");
                return 0;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[OrderService] ❌ Exception: {ex.Message}");
            Console.WriteLine($"[OrderService] ❌ Stack trace: {ex.StackTrace}");
            return 0;
        }
    }

    private OrdersPagedResponse GetDemoOrdersData(OrderFilterRequest request)
    {
        var allOrders = GetDemoOrders();
        
        // Apply filters
        var filteredOrders = allOrders.AsQueryable();
        
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            filteredOrders = filteredOrders.Where(o => 
                o.OrderNumber.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerEmail.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
        }
        
        if (!string.IsNullOrEmpty(request.Status))
        {
            filteredOrders = filteredOrders.Where(o => o.Status == request.Status);
        }
        
        if (request.StartDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.OrderDate.Date >= request.StartDate.Value.Date);
        }
        
        if (request.EndDate.HasValue)
        {
            filteredOrders = filteredOrders.Where(o => o.OrderDate.Date <= request.EndDate.Value.Date);
        }

        var totalCount = filteredOrders.Count();
        var orders = filteredOrders
            .OrderByDescending(o => o.OrderDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new OrdersPagedResponse
        {
            Orders = orders,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };
    }

    private List<Order> GetDemoOrders()
    {
        return new List<Order>
        {
            new Order
            {
                Id = 1,
                OrderNumber = "ORD-001",
                CustomerName = "Amadou Diallo",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+224 621 12 34 56",
                OrderDate = DateTime.Now.AddDays(-1),
                ShippingAddress = "Quartier Almamya, Rue KA-020",
                ShippingCity = "Conakry",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = "Mobile Money",
                PaymentStatus = "Payé",
                Status = "En attente",
                Subtotal = 45000,
                TotalAmount = 50000,
                Notes = "",
                SellerId = 1,
                SellerName = "Vendeur Test",
                Items = new List<OrderItem>
                {
                    new OrderItem { Id = 1, ProductId = 1, ProductName = "Smartphone XYZ", ProductImageUrl = "/images/products/smartphone.jpg", UnitPrice = 150000, Quantity = 1 }
                }
            },
            new Order
            {
                Id = 2,
                OrderNumber = "ORD-002",
                CustomerName = "Fatou Camara",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+221 77 123 45 67",
                OrderDate = DateTime.Now.AddDays(-2),
                ShippingAddress = "Rue 10 x Avenue Blaise Diagne",
                ShippingCity = "Dakar",
                ShippingMethod = "Livraison express",
                ShippingFee = 8500,
                PaymentMethod = "Carte bancaire",
                PaymentStatus = "Payé",
                Status = "Expédié",
                Subtotal = 70000,
                TotalAmount = 78500,
                Notes = "Client fidèle, livraison prioritaire",
                SellerId = 1,
                SellerName = "Vendeur Test",
                Items = new List<OrderItem>
                {
                    new OrderItem { Id = 2, ProductId = 2, ProductName = "T-shirt Coton", ProductImageUrl = "/images/products/tshirt.jpg", UnitPrice = 8000, Quantity = 2 },
                    new OrderItem { Id = 3, ProductId = 3, ProductName = "Robe d'Été", ProductImageUrl = "/images/products/dress.jpg", UnitPrice = 15000, Quantity = 1 }
                }
            }
        };
    }

    private static string MapOrderStatus(string apiStatus)
    {
        return apiStatus switch
        {
            "0" or "Pending" => "En attente",
            "1" or "Paid" => "Payé",
            "2" or "Shipped" => "Expédié",
            "3" or "Delivered" => "Livré",
            "4" or "Cancelled" => "Annulé",
            _ => "En attente"
        };
    }

    private static string MapPaymentStatus(string apiPaymentStatus)
    {
        return apiPaymentStatus switch
        {
            "0" or "Pending" => "En attente",
            "1" or "Processing" => "En cours",
            "2" or "Completed" => "Payé",
            "3" or "Failed" => "Échoué",
            "4" or "Cancelled" => "Annulé",
            "5" or "Refunded" => "Remboursé",
            _ => "En attente"
        };
    }

    private static string MapPaymentMethod(string apiPaymentMethod)
    {
        return apiPaymentMethod switch
        {
            "0" or "CashOnDelivery" => "Paiement à la livraison",
            "1" or "OrangeMoney" => "Orange Money",
            "2" or "Stripe" => "Carte bancaire",
            _ => "Carte bancaire"
        };
    }

    private static Order ConvertToOrder(ApiOrderDto apiOrder, int sellerId = 0)
    {
        return new Order
        {
            Id = apiOrder.Id,
            OrderNumber = $"ORD-{apiOrder.Id:D3}",
            CustomerName = apiOrder.ShippingAddress?.FullName ?? "Client API",
            CustomerEmail = apiOrder.UserId,
            CustomerPhone = apiOrder.ShippingAddress?.PhoneNumber ?? "",
            OrderDate = apiOrder.OrderDate,
            ShippingAddress = apiOrder.ShippingAddress?.Address ?? "",
            ShippingCity = apiOrder.ShippingAddress?.City ?? "",
            ShippingMethod = "Standard",
            ShippingFee = 0,
            PaymentMethod = MapPaymentMethod(apiOrder.PaymentMethod),
            Status = MapOrderStatus(apiOrder.Status),
            PaymentStatus = MapPaymentStatus(apiOrder.PaymentStatus),
            Subtotal = apiOrder.TotalAmount,
            TotalAmount = apiOrder.TotalAmount,
            Notes = "",
            Items = apiOrder.OrderItems?.Select(item => new OrderItem
            {
                Id = item.Id,
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                ProductImageUrl = item.ImageUrl ?? "",
                UnitPrice = item.UnitPrice,
                Quantity = item.Quantity
            }).ToList() ?? new List<OrderItem>(),
            SellerId = sellerId,
            SellerName = "Vendeur API"
        };
    }
}

public class SellerDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
}
